// IMPORTANT:
// After a successful login or signup, you MUST call:
//   await AuthService.saveToken(tokenFromBackend);
// where tokenFromBackend is the JWT or access token returned by your backend.
//
// If you do not call saveToken, the profile page will not work.

import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';

class AuthService {
  // Helper for debugging: check if a token is saved
  static Future<void> debugPrintToken() async {
    final token = await getToken();
    print('DEBUG: Current saved token: ${token ?? 'null'}');
  }
  static const String baseUrl = 'https://g-gist-api.onrender.com/api/auth';

  // Save token after login/signup
  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  // Get token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Fetch current user profile
  static Future<UserProfile?> fetchCurrentUser() async {
    final token = await getToken();
    print('DEBUG: Loaded token: '
        '${token != null ? token.substring(0, token.length > 8 ? 8 : token.length) : 'null'}');
    if (token == null) {
      print('DEBUG: No token found.');
      return null;
    }
    final response = await http.get(
      Uri.parse('$baseUrl/me'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );
    print('DEBUG: /me status: ${response.statusCode}');
    print('DEBUG: /me body: ${response.body}');
    if (response.statusCode == 200) {
      final body = jsonDecode(response.body);
      if (body['success'] == true && body['data'] != null) {
        return UserProfile.fromJson(body['data']);
      }
    }
    return null;
  }
}
