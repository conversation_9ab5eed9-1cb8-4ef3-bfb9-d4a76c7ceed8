class UserProfile {
  final String id;
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final String bio;
  final String? profileImageUrl;
  final bool isVerified;
  final String role;
  final int followerCount;
  final int followingCount;

  UserProfile({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.bio,
    required this.profileImageUrl,
    required this.isVerified,
    required this.role,
    required this.followerCount,
    required this.followingCount,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['_id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      bio: json['bio'] ?? '',
      profileImageUrl: json['profilePicture'] != null && json['profilePicture']['url'] != null
          ? json['profilePicture']['url']
          : null,
      isVerified: json['isVerified'] ?? false,
      role: json['role'] ?? '',
      followerCount: json['followerCount'] ?? 0,
      followingCount: json['followingCount'] ?? 0,
    );
  }
}
