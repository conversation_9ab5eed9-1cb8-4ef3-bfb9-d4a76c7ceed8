
import 'package:flutter/material.dart';

class Overview extends StatefulWidget {
  const Overview({super.key});

  @override
  State<Overview> createState() => _OverviewState();
}

class _OverviewState extends State<Overview> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('The Grand Budapest Hotel'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0.5,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.network(
              'https://images.unsplash.com/photo-1489599162163-e3d9b6b4c5b5?w=800&h=400&fit=crop',
              height: 200,
              width: double.infinity,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  height: 200,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: const Center(child: CircularProgressIndicator()),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 200,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: const Icon(Icons.error, size: 50),
                );
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: const [
                Text('2014', style: TextStyle(fontWeight: FontWeight.bold)),
                SizedBox(width: 10),
                Text('• Comedy/Drama'),
                SizedBox(width: 10),
                Text('• 1h 39m'),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: const [
                Icon(Icons.star, color: Colors.amber, size: 20),
                SizedBox(width: 5),
                Text('4.8'),
                SizedBox(width: 10),
                Text('92% (2.1k Reviews)'),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () {},
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Watch Trailer'),
                ),
                const SizedBox(width: 10),
                OutlinedButton.icon(
                  onPressed: () {},
                  icon: const Icon(Icons.bookmark_outline),
                  label: const Text('Add to Watchlist'),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text('Synopsis',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            const Text(
              'In the 1930s, the Grand Budapest Hotel is a popular European ski resort, presided over by concierge Gustave H. '
                  'When one of Gustave’s lovers dies mysteriously, Gustave finds himself the recipient of a priceless painting '
                  'and the chief suspect in her murder.',
              style: TextStyle(height: 1.5),
            ),
            const SizedBox(height: 24),
            const Text('Cast & Crew',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Row(
              children: const [
                CircleAvatar(
                  backgroundImage: NetworkImage('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face'),
                  radius: 30,
                ),
                SizedBox(width: 12),
                Text('Ralph Fiennes\n(Gustave H.)'),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: const [
                CircleAvatar(
                  backgroundImage: NetworkImage('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face'),
                  radius: 30,
                ),
                SizedBox(width: 12),
                Text('Tony Revolori\n(Zero)'),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: const [
                CircleAvatar(
                  backgroundImage: NetworkImage('https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face'),
                  radius: 30,
                ),
                SizedBox(width: 12),
                Text('Saoirse Ronan\n(Agatha)'),
              ],
            ),
            const SizedBox(height: 24),
            const Text('Additional Information',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Wrap(
              spacing: 10,
              children: const [
                Chip(label: Text('Comedy')),
                Chip(label: Text('Drama')),
                Chip(label: Text('Adventure')),
              ],
            ),
            const SizedBox(height: 12),
            const Text('Release Date: March 7, 2014'),
            const Text('Language: English'),
            const Text('Runtime: 1h 39m'),
          ],
        ),
      ),
    );
  }
}
