import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:g_gistmobile/models/user_profile.dart';
import 'package:g_gistmobile/services/auth_service.dart';
import 'package:shared_preferences/shared_preferences.dart';


class ProfileStat extends StatelessWidget {
  final String title;
  final String subtitle;

  const ProfileStat({
    Key? key,
    required this.title,
    required this.subtitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: const TextStyle(color: Colors.grey),
        ),
      ],
    );
  }
}

class MovieCard extends StatelessWidget {
  final String title;
  final String date;
  final String imageUrl;

  const MovieCard({
    Key? key,
    required this.title,
    required this.date,
    required this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.hardEdge,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              width: double.infinity,
              loadingBuilder: (context, child, progress) {
                if (progress == null) return child;
                return const Center(child: CircularProgressIndicator());
              },
              errorBuilder: (context, error, stackTrace) =>
                  const Center(child: Icon(Icons.broken_image)),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                const Row(
                  children: [
                    Icon(Icons.star, color: Colors.amber, size: 16),
                    Icon(Icons.star, color: Colors.amber, size: 16),
                    Icon(Icons.star, color: Colors.amber, size: 16),
                    Icon(Icons.star, color: Colors.amber, size: 16),
                    Icon(Icons.star_half, color: Colors.amber, size: 16),
                  ],
                ),
                const SizedBox(height: 4),
                Text(date, style: const TextStyle(color: Colors.grey, fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _bioController = TextEditingController();

  @override
  void dispose() {
    _usernameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  void _showEditProfileModal() {
    _usernameController.text = _userProfile?.username ?? '';
    _bioController.text = _userProfile?.bio ?? '';
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text("Edit Profile", style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20)),
              const SizedBox(height: 20),
              TextField(
                controller: _usernameController,
                decoration: const InputDecoration(
                  labelText: "Username",
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _bioController,
                decoration: const InputDecoration(
                  labelText: "Bio",
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _saveProfileChanges,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text("Save Changes"),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveProfileChanges() async {
    final newUsername = _usernameController.text.trim();
    final newBio = _bioController.text.trim();
    Navigator.of(context).pop();
    final token = await AuthService.getToken();
    if (token == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Not authenticated. Please log in again.'), backgroundColor: Colors.red),
      );
      return;
    }
    final response = await http.put(
      Uri.parse('https://g-gist-api.onrender.com/api/users/profile'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'username': newUsername,
        'bio': newBio,
      }),
    );
    if (response.statusCode == 200) {
      // Refresh profile info
      await _fetchProfile();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profile updated!'), backgroundColor: Colors.green),
      );
    } else {
      print('DEBUG: Profile update failed. Status: ${response.statusCode}');
      print('DEBUG: Response body: ${response.body}');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update profile: ${response.body}'), backgroundColor: Colors.red),
      );
    }
  }
  UserProfile? _userProfile;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _fetchProfile();
  }

  Future<void> _fetchProfile() async {
    setState(() => _loading = true);
    final user = await AuthService.fetchCurrentUser();
    if (!mounted) return;
    if (user == null) {
      print('DEBUG: User profile is null after fetch.');
    }
    setState(() {
      _userProfile = user;
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text("Profile"),
        backgroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
        actions: [
          PopupMenuButton<int>(
            icon: const Icon(Icons.settings),
            offset: const Offset(0, 40), // Show menu directly under the icon
            onSelected: (value) async {
              if (value == 1) {
                // Sign out
                final prefs = await SharedPreferences.getInstance();
                await prefs.remove('auth_token');
                if (!mounted) return;
                Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 1,
                child: Row(
                  children: const [
                    Icon(Icons.logout, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Sign Out', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _userProfile == null
              ? const Center(child: Text("Failed to load profile."))
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      Stack(
                        alignment: Alignment.bottomRight,
                        children: [
                          _userProfile!.profileImageUrl != null &&
                                  _userProfile!.profileImageUrl!.isNotEmpty
                              ? CircleAvatar(
                                  radius: 45,
                                  backgroundImage:
                                      NetworkImage(_userProfile!.profileImageUrl!),
                                )
                              : CircleAvatar(
                                  radius: 45,
                                  backgroundColor: Colors.blue.shade100,
                                  child: Text(
                                    _userProfile!.username.isNotEmpty
                                        ? _userProfile!.username[0].toUpperCase()
                                        : '?',
                                    style: const TextStyle(
                                      fontSize: 32,
                                      color: Colors.blue,
                                      fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                _userProfile!.username,
                                style: const TextStyle(
                                    fontSize: 22, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 6),
                              Text(
                                _userProfile!.bio.isNotEmpty
                                    ? _userProfile!.bio
                                    : "No bio yet.",
                                style: const TextStyle(color: Colors.grey),
                              ),
                              const SizedBox(height: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  OutlinedButton.icon(
                                    onPressed: _showEditProfileModal,
                                    icon: const Icon(Icons.edit, size: 18, color: Colors.blue),
                                    label: const Text("Edit Profile", style: TextStyle(color: Colors.blue)),
                                    style: OutlinedButton.styleFrom(
                                      side: const BorderSide(color: Colors.blue),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                      // Followers, Following
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ProfileStat(
                            title: _userProfile!.followerCount.toString(),
                            subtitle: "Followers",
                          ),
                          ProfileStat(
                            title: _userProfile!.followingCount.toString(),
                            subtitle: "Following",
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Movies, Rating, Watch Time (static for now)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: const [
                          ProfileStat(title: "394", subtitle: "Movies"),
                          ProfileStat(title: "4.2/5", subtitle: "Avg Rating"),
                          ProfileStat(title: "789h", subtitle: "Watch Time"),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Tabs
                      DefaultTabController(
                        length: 4,
                        child: Column(
                          children: [
                            const TabBar(
                              labelColor: Colors.blue,
                              unselectedLabelColor: Colors.grey,
                              indicatorColor: Colors.blue,
                              tabs: [
                                Tab(text: "Watched"),
                                Tab(text: "Watchlist"),
                                Tab(text: "Reviews"),
                                Tab(text: "Discussions"),
                              ],
                            ),
                            SizedBox(
                              height: 400,
                              child: TabBarView(
                                children: [
                                  GridView.builder(
                                    gridDelegate:
                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      crossAxisSpacing: 10,
                                      mainAxisSpacing: 10,
                                    ),
                                    padding: const EdgeInsets.all(10),
                                    itemCount: 4,
                                    itemBuilder: (context, index) {
                                      final movies = [
                                        {
                                          'title': 'Inception',
                                          'date': 'Oct 2023',
                                          'imageUrl':
                                              'https://picsum.photos/200/300?1',
                                        },
                                        {
                                          'title': 'Interstellar',
                                          'date': 'Sep 2023',
                                          'imageUrl':
                                              'https://picsum.photos/200/300?2',
                                        },
                                        {
                                          'title': 'Dunkirk',
                                          'date': 'Aug 2023',
                                          'imageUrl':
                                              'https://picsum.photos/200/300?3',
                                        },
                                        {
                                          'title': 'Tenet',
                                          'date': 'Jul 2023',
                                          'imageUrl':
                                              'https://picsum.photos/200/300?4',
                                        },
                                      ];
                                      final movie = movies[index];
                                      return MovieCard(
                                        title: movie['title']!,
                                        date: movie['date']!,
                                        imageUrl: movie['imageUrl']!,
                                      );
                                    },
                                  ),
                                  const Center(child: Text("Watchlist")),
                                  const Center(child: Text("Reviews")),
                                  const Center(child: Text("Discussions")),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
  // Bottom nav bar is now handled by MainShell
    );
  }
}
