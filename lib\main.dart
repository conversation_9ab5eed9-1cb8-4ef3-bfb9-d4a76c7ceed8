import 'package:flutter/material.dart';
import 'package:g_gistmobile/auth/authscreen.dart';
import 'package:g_gistmobile/auth/forgotpassword.dart';
import 'package:g_gistmobile/auth/login.dart';
import 'package:g_gistmobile/auth/signup.dart';
import 'package:g_gistmobile/pages/community.dart';
import 'package:g_gistmobile/pages/discussion.dart';
import 'package:g_gistmobile/pages/filmdatabase.dart';
import 'package:g_gistmobile/pages/home.dart';
import 'package:g_gistmobile/pages/notification.dart';
import 'package:g_gistmobile/pages/overview.dart';
import 'package:g_gistmobile/pages/profile.dart';
import 'package:g_gistmobile/pages/review.dart';
import 'package:g_gistmobile/pages/splashscreen.dart';
import 'package:g_gistmobile/widgets/custom_bottom_nav_bar.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: const Overview(),
      routes: {
        '/profile': (context) => const ProfilePage(),
        '/splash': (context) => const Splashscreen(),
        '/home': (context) => const Home(),
        '/discussion': (context) => const Discussion(),
        '/review': (context) => const ReviewPage(),
        '/community': (context) => const Community(),
        '/notification': (context) => const NotificationPage(),
        '/filmdatabase': (context) => const Filmdatabase(),
        '/overview': (context) => const Overview(),
        '/forgotpassword': (context) => const ForgotPassword(),
        '/signup': (context) => const Signup(),
        '/login': (context) => const Login(),
        '/authscreen': (context) => const AuthScreen(),
      },
    );
  }
}

class MainShell extends StatefulWidget {
  const MainShell({super.key});

  @override
  State<MainShell> createState() => _MainShellState();
}

class _MainShellState extends State<MainShell> {
  int _selectedIndex = 0;

  static final List<Widget> _pages = <Widget>[
    Home(),
    Filmdatabase(),
    Community(),
    Discussion(),
    ProfilePage(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: CustomBottomNavBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        itemCount: 5,
      ),
    );
  }
}
