import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class Movie {
  final String id;
  final String title;
  final String releaseDate;
  final String posterPath;
  final double rating;
  final int voteCount;

  Movie({
    required this.id,
    required this.title,
    required this.releaseDate,
    required this.posterPath,
    required this.rating,
    required this.voteCount,
  });

  factory Movie.fromJson(Map<String, dynamic> json) {
    return Movie(
      id: json['id'] ?? '',
      title: json['title'] ?? 'Unknown Title',
      releaseDate: json['releaseDate'] ?? '',
      posterPath: json['posterPath'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      voteCount: json['voteCount'] ?? 0,
    );
  }

  String get year {
    if (releaseDate.isEmpty) return 'TBA';
    return releaseDate.substring(0, 4);
  }
}

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  List<Movie> featuredMovies = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    fetchFeaturedMovies();
  }

  Future<void> fetchFeaturedMovies() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      final response = await http.get(
        Uri.parse('https://g-gist-api.onrender.com/api/films/trending'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> moviesJson = data['data'];
          setState(() {
            // Take only first 6 movies for the carousel
            featuredMovies =
                moviesJson.take(6).map((json) => Movie.fromJson(json)).toList();
            isLoading = false;
          });
        } else {
          throw Exception('API returned success: false');
        }
      } else {
        throw Exception('Failed to load movies: ${response.statusCode}');
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load movies: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: const Color(0xFF6B5EFF),
        title: const Text("G-Gist",
            style: TextStyle(fontWeight: FontWeight.bold)),
        actions: const [
          Icon(Icons.search),
          SizedBox(width: 16),
          Icon(Icons.notifications_none),
          SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Featured Movies Carousel
            const Text("Featured Movies",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),

            SizedBox(
              height: 220, // Reduced height to prevent overflow
              child: isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : errorMessage != null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error_outline,
                                  size: 48, color: Colors.grey),
                              const SizedBox(height: 8),
                              Text(
                                errorMessage!,
                                textAlign: TextAlign.center,
                                style: const TextStyle(color: Colors.grey),
                              ),
                              const SizedBox(height: 8),
                              ElevatedButton(
                                onPressed: fetchFeaturedMovies,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : featuredMovies.isEmpty
                          ? const Center(
                              child: Text(
                                'No movies available',
                                style: TextStyle(color: Colors.grey),
                              ),
                            )
                          : ListView.builder(
                              scrollDirection: Axis.horizontal,
                              padding:
                                  EdgeInsets.zero, // Remove default padding
                              itemCount: featuredMovies.length,
                              itemBuilder: (context, index) {
                                return _buildMovieCard(featuredMovies[index]);
                              },
                            ),
            ),

            const SizedBox(height: 20),

            // Category Chips
            const Text("Browse by Genre",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: const [
                Chip(
                  label: Text("Action"),
                  backgroundColor: Color(0xFFE3F2FD),
                ),
                Chip(
                  label: Text("Comedy"),
                  backgroundColor: Color(0xFFF3E5F5),
                ),
                Chip(
                  label: Text("Drama"),
                  backgroundColor: Color(0xFFE8F5E8),
                ),
                Chip(
                  label: Text("Sci-Fi"),
                  backgroundColor: Color(0xFFFFF3E0),
                ),
                Chip(
                  label: Text("Thriller"),
                  backgroundColor: Color(0xFFFFEBEE),
                ),
                Chip(
                  label: Text("Romance"),
                  backgroundColor: Color(0xFFF1F8E9),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Feed Posts Header
            const Text("Recent Reviews",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),

            // Feed Posts
            _buildPost(
              userName: "Sarah Mitchell",
              time: "2h ago",
              movieTitle: "Poor Things",
              movieImage:
                  "https://via.placeholder.com/100x150/4ECDC4/FFFFFF?text=Poor+Things",
              review:
                  "A masterpiece that challenges your perception of reality. The cinematography is breathtaking, and Emma Stone delivers a phenomenal performance that will stay with you long after the credits roll.",
              likes: 234,
              comments: 45,
            ),

            const SizedBox(height: 16),

            _buildPost(
              userName: "Michael Chen",
              time: "4h ago",
              movieTitle: "The Zone of Interest",
              movieImage:
                  "https://via.placeholder.com/100x150/F9CA24/FFFFFF?text=Zone",
              review:
                  "A thought-provoking drama that keeps you guessing until the very end. The plot twists are brilliant, and the sound design creates an incredibly unsettling atmosphere.",
              likes: 156,
              comments: 28,
            ),

            const SizedBox(height: 16),

            _buildPost(
              userName: "Emma Rodriguez",
              time: "6h ago",
              movieTitle: "Oppenheimer",
              movieImage:
                  "https://via.placeholder.com/100x150/45B7D1/FFFFFF?text=Oppenheimer",
              review:
                  "Nolan's biographical masterpiece is both visually stunning and emotionally powerful. The performances are outstanding across the board.",
              likes: 312,
              comments: 67,
            ),

            const SizedBox(height: 16),

            _buildPost(
              userName: "James Wilson",
              time: "8h ago",
              movieTitle: "Past Lives",
              movieImage:
                  "https://via.placeholder.com/100x150/A29BFE/FFFFFF?text=Past+Lives",
              review:
                  "A beautiful meditation on love, loss, and the paths we choose in life. Subtle yet deeply moving storytelling at its finest.",
              likes: 189,
              comments: 34,
            ),

            // Add some bottom padding for better scrolling
            const SizedBox(height: 100),
          ],
        ),
      ),

      // Floating Action Button
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  // Improved Featured Movie Card
  Widget _buildMovieCard(Movie movie) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(
          right: 12), // Only right margin to avoid left spacing
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // Prevent overflow
        children: [
          // Movie Poster with shadow
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                movie.posterPath,
                height: 160, // Reduced height to fit in container
                width: 140,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    height: 160,
                    width: 140,
                    color: Colors.grey[300],
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 160,
                    width: 140,
                    color: Colors.grey[300],
                    child:
                        const Icon(Icons.movie, size: 40, color: Colors.grey),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 6), // Reduced spacing
          Text(
            movie.title,
            style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 13), // Slightly smaller font
            maxLines: 1, // Reduced to 1 line to save space
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            movie.year,
            style: const TextStyle(
                color: Colors.grey, fontSize: 11), // Smaller font
          ),
          const SizedBox(height: 2), // Reduced spacing
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.star,
                  color: Colors.amber, size: 12), // Smaller icon
              const SizedBox(width: 2),
              Text(
                movie.rating.toStringAsFixed(1),
                style: const TextStyle(
                    fontSize: 11,
                    color: Colors.grey,
                    fontWeight: FontWeight.w500), // Smaller font
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Enhanced Post Card
  Widget _buildPost({
    required String userName,
    required String time,
    required String movieTitle,
    required String movieImage,
    required String review,
    required int likes,
    required int comments,
  }) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 3,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info
            Row(
              children: [
                const CircleAvatar(
                  backgroundImage:
                      NetworkImage("https://via.placeholder.com/50"),
                  radius: 20,
                ),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(userName,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 15)),
                    Text(time,
                        style:
                            const TextStyle(color: Colors.grey, fontSize: 12)),
                  ],
                ),
                const Spacer(),
                Icon(Icons.more_vert, color: Colors.grey[600], size: 20),
              ],
            ),
            const SizedBox(height: 16),

            // Post Content
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      movieImage,
                      width: 70,
                      height: 105,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 70,
                          height: 105,
                          color: Colors.grey[300],
                          child: const Icon(Icons.movie, color: Colors.grey),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(movieTitle,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16)),
                      const SizedBox(height: 8),
                      Text(review,
                          maxLines: 4,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(height: 1.4)),
                      const SizedBox(height: 8),
                      Row(
                        children: const [
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          Icon(Icons.star_half, color: Colors.amber, size: 16),
                          SizedBox(width: 4),
                          Text("4.5",
                              style:
                                  TextStyle(fontSize: 12, color: Colors.grey)),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Interaction Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(children: [
                  const Icon(Icons.favorite_border, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text("$likes", style: const TextStyle(color: Colors.grey))
                ]),
                Row(children: [
                  const Icon(Icons.chat_bubble_outline, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text("$comments", style: const TextStyle(color: Colors.grey))
                ]),
                const Icon(Icons.share_outlined, color: Colors.grey),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
